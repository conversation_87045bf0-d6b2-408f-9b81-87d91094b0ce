<!-- <PERSON><PERSON> -->
<div class="px-4 py-4 bg-custom-green">
    <h2 class="text-lg font-bold text-custom-darkest px-3 flex items-center">
        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
        Menu
    </h2>
</div>

<!-- Navigation Menu -->
<nav class="flex-1 px-4 py-4 space-y-2">
    <!-- Profile -->
    <a href="{{ route('profile.show') }}" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg {{ request()->routeIs('profile*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest' }}">
        <x-svg-icon name="Profile" class="w-5 h-5 mr-3" />
        Profile
    </a>

    <!-- Campuses -->
    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-custom-darkest hover:bg-custom-second-darkest">
        <x-svg-icon name="Campuses" class="w-5 h-5 mr-3" />
        Campuses
    </a>

    <!-- Organizations & Pages -->
    <a href="{{ route('organizations.index') }}" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg {{ request()->routeIs('organizations*') || request()->routeIs('pages*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest' }}">
        <x-svg-icon name="Organizations" class="w-5 h-5 mr-3" />
        Organizations
    </a>

    <!-- Student Groups -->
    <a href="{{ route('groups.index') }}" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg {{ request()->routeIs('groups*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest' }}">
        <x-svg-icon name="Groups" class="w-5 h-5 mr-3" />
        Groups
    </a>

    <!-- Follow Management -->
    <div class="space-y-1" x-data="{
        open: localStorage.getItem('connectionsDropdownOpen') === 'true',
        toggle() {
            this.open = !this.open;
            localStorage.setItem('connectionsDropdownOpen', this.open);
        }
    }">
        <button type="button" @click="toggle()" class="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 focus:outline-none focus:bg-custom-lightest rounded-lg">
            <x-svg-icon name="Connections" class="w-5 h-5 mr-3" />
            <span class="flex-1 text-left">Connections</span>
            <svg :class="{'transform rotate-90': open}" class="w-4 h-4 ml-auto transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
        <div x-show="open" x-transition class="space-y-1 pl-2">
            <a href="{{ route('follow-management.followers') }}" class="flex items-center px-6 py-2 text-sm rounded-lg {{ request()->routeIs('follow-management.followers') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest' }}">
                <x-svg-icon name="Followers" class="w-4 h-4 mr-3" />
                My Followers
            </a>
            <a href="{{ route('follow-management.following') }}" class="flex items-center px-6 py-2 text-sm rounded-lg {{ request()->routeIs('follow-management.following') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest' }}">
                <x-svg-icon name="Following" class="w-4 h-4 mr-3" />
                Following
            </a>
        </div>
    </div>

    <!-- Find Scholarships -->
    <a href="{{ route('scholarships.index') }}" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg {{ request()->routeIs('scholarships*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest' }}">
        <x-svg-icon name="Scholarships" class="w-4 h-4 mr-3" />
        Scholarships
    </a>

    <!-- Admin Dashboard -->
    @if(auth()->user()->hasManagementAccess())
        <a href="{{ route('admin.dashboard') }}" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-custom-darkest hover:bg-custom-second-darkest">
            <x-svg-icon name="Admin" class="w-5 h-5 mr-3" />
            Admin Dashboard
        </a>
    @endif
</nav>

    <!-- Logout -->
    <form method="POST" action="{{ route('logout') }}">
        @csrf
        <button type="submit" class="flex items-center w-full px-3 py-2 text-sm font-medium text-custom-darkest rounded-lg hover:bg-custom-second-darkest">
            <x-svg-icon name="Lagout" class="w-5 h-5 mr-3" />
            Logout
        </button>
    </form>
</nav>



